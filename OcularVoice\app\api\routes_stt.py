"""
API routes for Speech-to-Text functionality.
Implements MCP (Model Context Protocol) standards.
"""
import os
from fastapi import APIRouter, UploadFile, File, Form, HTTPException, BackgroundTasks
from fastapi.responses import JSONResponse
from typing import Optional, Dict, Any
import logging

from ..core.context_manager import context_manager
from ..modules.speech import stt_engine
from ..utils.audio_utils import save_uploaded_audio
from ..utils.common import generate_uuid, format_response, handle_exception

# Configure logger
logger = logging.getLogger("ocularvoice.api.stt")

# Create router
router = APIRouter(
    prefix="/speech/stt",
    tags=["speech-to-text"],
    responses={404: {"description": "Not found"}},
)

@router.get("/status")
async def get_status():
    """
    Get the status of the STT engine.
    
    Returns:
        Dict: Status information
    """
    try:
        status = stt_engine.status()
        return format_response(success=True, data=status)
    except Exception as e:
        handle_exception(e, "Error getting STT status")

@router.post("/infer")
async def infer(
    audio: UploadFile = File(...),
    session_id: Optional[str] = Form(None),
    language: Optional[str] = Form(None),
    task: Optional[str] = Form("transcribe")
):
    """
    Transcribe audio to text using the STT engine.
    
    Args:
        audio: The audio file to transcribe
        session_id: Optional session ID
        language: Optional language code
        task: Task to perform, either "transcribe" or "translate" (to English)
        
    Returns:
        Dict: Transcription result
    """
    try:
        # Generate or validate session ID
        session_id = context_manager.create_session(session_id)
        
        # Save uploaded file
        file_ext = os.path.splitext(audio.filename)[1] if audio.filename else ".m4a"
        audio_data = await audio.read()
        audio_path = save_uploaded_audio(audio_data, file_ext, session_id)
        
        # Log the request
        logger.info(f"STT inference request for session {session_id}, language: {language}, task: {task}")
        
        # Process with STT engine
        result = stt_engine.infer(
            session_id=session_id,
            audio_path=audio_path,
            language=language,
            task=task
        )
        
        # Store transcription in context
        if result.get("success", False) and "text" in result:
            context_manager.update_context(session_id, {
                "last_transcription": result["text"],
                "last_language": result.get("language")
            })
            
            # Add message to chat history
            context_manager.add_message(
                session_id=session_id,
                role="user",
                content=result["text"],
                audio_path=audio_path,
                language=result.get("language")
            )
        
        return result
    except Exception as e:
        handle_exception(e, "Error processing STT inference")

@router.post("/stream")
async def stream(
    session_id: Optional[str] = Form(None),
    language: Optional[str] = Form(None)
):
    """
    Stream audio for transcription (placeholder - not supported by Whisper).
    
    Args:
        session_id: Optional session ID
        language: Optional language code
        
    Returns:
        Dict: Error response as streaming is not supported
    """
    # Generate or validate session ID
    session_id = context_manager.create_session(session_id)
    
    # Log the request
    logger.warning(f"STT streaming requested but not supported (session: {session_id})")
    
    # Return not supported response
    return {
        "success": False,
        "error": "Streaming not supported by Whisper model",
        "session_id": session_id
    }
