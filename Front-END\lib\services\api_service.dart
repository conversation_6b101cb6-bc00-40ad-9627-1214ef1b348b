import 'dart:convert';
import 'dart:io';
import 'dart:async';
import 'dart:math';
import 'package:flutter/foundation.dart';
import 'package:http/http.dart' as http;

class ApiService {
  // Base URL of the OcularVoice server
  // For Android emulator use: 'http://********:8000'
  // For iOS simulator use: 'http://localhost:8000'
  // For physical devices, use your computer's IP address on the network
  static const String baseUrl =
      'http://**************:8001'; // Updated to match your current Wi-Fi IP address

  // Session ID for maintaining conversation context
  String? sessionId;

  // API endpoints following MCP (Model Context Protocol)
  static const String sttInferEndpoint = '/speech/stt/infer';
  static const String ttsInferEndpoint = '/speech/tts/infer';
  static const String objectDetectionEndpoint = '/object_detection/infer';

  // Constructor - generates a session ID if none exists
  ApiService() {
    // Generate a session ID if none exists
    sessionId ??= _generateSessionId();
    if (kDebugMode) {
      debugPrint('Initialized ApiService with session ID: $sessionId');
    }
  }

  // Generate a simple session ID
  String _generateSessionId() {
    final random = Random.secure();
    const chars = 'abcdefghijklmnopqrstuvwxyz0123456789';
    return List.generate(32, (_) => chars[random.nextInt(chars.length)]).join();
  }

  // Send voice recording to the server for processing using MCP-compliant endpoint
  Future<Map<String, dynamic>> sendVoiceRecording(String audioPath) async {
    try {
      // Check if the audio file exists
      final audioFile = File(audioPath);
      if (!await audioFile.exists()) {
        return {
          'success': false,
          'error': 'Audio file not found at path: $audioPath',
        };
      }

      // Check if server is available
      try {
        // Try to ping the server with a short timeout
        if (kDebugMode) {
          debugPrint('Attempting to connect to server at $baseUrl');
        }

        final response = await http
            .get(Uri.parse(baseUrl), headers: {'Connection': 'close'})
            .timeout(const Duration(seconds: 5)); // Increased timeout

        // If we get here, server is available
        if (kDebugMode) {
          debugPrint('Server is available at $baseUrl');
          debugPrint('Response status code: ${response.statusCode}');
          debugPrint('Response body: ${response.body}');
        }
      } catch (pingError) {
        // Server not available, show error
        if (kDebugMode) {
          debugPrint('Server not available: $pingError');
          debugPrint('Error type: ${pingError.runtimeType}');
          debugPrint('Full error details: $pingError');
        }
        return {
          'success': false,
          'error':
              'Cannot connect to server at $baseUrl. Please make sure the server is running.',
        };
      }

      // Create multipart request with timeout (using legacy endpoint for now)
      var request = http.MultipartRequest(
        'POST',
        Uri.parse('$baseUrl/voice-chat/'),
      );

      // Add session ID if available
      if (sessionId != null) {
        request.fields['session_id'] = sessionId!;
      }

      // Add audio file
      request.files.add(await http.MultipartFile.fromPath('audio', audioPath));

      // Send request with timeout
      if (kDebugMode) {
        debugPrint('Sending voice recording to $baseUrl/voice-chat/');
        debugPrint('Audio file path: $audioPath');
        debugPrint('Session ID: $sessionId');
      }

      var streamedResponse = await request.send().timeout(
        const Duration(seconds: 15), // Increased timeout
        onTimeout: () {
          if (kDebugMode) {
            debugPrint('Connection to server timed out after 15 seconds');
          }
          throw TimeoutException(
            'Connection to server timed out. Please check if the server is running at $baseUrl',
          );
        },
      );

      if (kDebugMode) {
        debugPrint('Received response from server');
        debugPrint('Response status code: ${streamedResponse.statusCode}');
        debugPrint('Response headers: ${streamedResponse.headers}');
      }

      var response = await http.Response.fromStream(streamedResponse);

      // Parse JSON response
      if (kDebugMode) {
        debugPrint('Response body: ${response.body}');
      }

      Map<String, dynamic> responseData;
      try {
        responseData = json.decode(response.body);
        if (kDebugMode) {
          debugPrint('Successfully parsed JSON response');
          debugPrint('Response data: $responseData');
        }
      } catch (e) {
        if (kDebugMode) {
          debugPrint('Error parsing JSON response: $e');
          debugPrint('Response body was: ${response.body}');
        }
        return {
          'success': false,
          'error': 'Invalid response from server: ${e.toString()}',
        };
      }

      // Get session ID from response if not already set
      if (sessionId == null && responseData['session_id'] != null) {
        sessionId = responseData['session_id'];
        if (kDebugMode) {
          debugPrint('Set session ID to: $sessionId');
        }
      }

      // Extract transcription and language from JSON response (Legacy format)
      if (response.statusCode == 200) {
        // Legacy response format
        final transcription =
            responseData['transcription'] ?? 'Audio processed successfully';
        final language = responseData['language'] ?? 'en';
        final aiResponse = responseData['response'] ?? '';

        if (kDebugMode) {
          debugPrint('Transcription: $transcription');
          debugPrint('Detected language: $language');
          debugPrint('AI Response: $aiResponse');
        }

        // Return success with transcription data
        return {
          'success': true,
          'data': {
            'text': transcription,
            'language': language,
            'response': aiResponse,
          },
        };
      }

      // Check if response contains error
      if (response.statusCode != 200) {
        return {
          'success': false,
          'error':
              responseData['detail'] ?? 'Server error: ${response.statusCode}',
        };
      }

      return {'success': true, 'data': responseData};
    } catch (e) {
      // Provide more specific error messages for common issues
      String errorMessage = e.toString();

      if (e is SocketException) {
        errorMessage =
            'Cannot connect to server at $baseUrl. Please check if the server is running.';
      } else if (e is TimeoutException) {
        errorMessage =
            'Connection to server timed out. Please check if the server is running at $baseUrl';
      } else if (e is FormatException) {
        errorMessage =
            'Invalid response format from server. The server might be running but returning unexpected data.';
      }

      return {'success': false, 'error': errorMessage};
    }
  }

  // Send text message to the server using legacy endpoint
  Future<Map<String, dynamic>> sendTextMessage(String message) async {
    try {
      final response = await http.post(
        Uri.parse('$baseUrl/text-chat/'),
        headers: {'Content-Type': 'application/json'},
        body: json.encode({'message': message, 'session_id': sessionId}),
      );

      final responseData = json.decode(response.body);

      // Update session ID if provided
      if (responseData['session_id'] != null) {
        sessionId = responseData['session_id'];
      }

      if (response.statusCode != 200) {
        return {
          'success': false,
          'error': responseData['detail'] ?? 'Unknown error occurred',
        };
      }

      return {'success': true, 'data': responseData};
    } catch (e) {
      return {'success': false, 'error': e.toString()};
    }
  }

  // Send image for analysis using legacy endpoint
  Future<Map<String, dynamic>> sendImageForAnalysis(
    File imageFile, {
    String? message,
  }) async {
    try {
      // Create multipart request
      var request = http.MultipartRequest(
        'POST',
        Uri.parse('$baseUrl/image-analysis/'),
      );

      // Add session ID if available
      if (sessionId != null) {
        request.fields['session_id'] = sessionId!;
      }

      // Add message if provided
      if (message != null) {
        request.fields['message'] = message;
      }

      // Add image file
      request.files.add(
        await http.MultipartFile.fromPath('image', imageFile.path),
      );

      // Send request
      var streamedResponse = await request.send();
      var response = await http.Response.fromStream(streamedResponse);

      final responseData = json.decode(response.body);

      // Update session ID if provided
      if (responseData['session_id'] != null) {
        sessionId = responseData['session_id'];
      }

      if (response.statusCode != 200) {
        return {
          'success': false,
          'error': responseData['detail'] ?? 'Unknown error occurred',
        };
      }

      return {'success': true, 'data': responseData};
    } catch (e) {
      return {'success': false, 'error': e.toString()};
    }
  }

  // Get chat history
  Future<Map<String, dynamic>> getChatHistory() async {
    if (sessionId == null) {
      return {'success': false, 'error': 'No active session'};
    }

    try {
      final response = await http.get(
        Uri.parse('$baseUrl/chat-history/$sessionId'),
      );

      final responseData = json.decode(response.body);

      if (response.statusCode != 200) {
        return {
          'success': false,
          'error': responseData['detail'] ?? 'Unknown error occurred',
        };
      }

      return {'success': true, 'data': responseData};
    } catch (e) {
      return {'success': false, 'error': e.toString()};
    }
  }

  // Clear chat history
  Future<Map<String, dynamic>> clearChatHistory() async {
    if (sessionId == null) {
      return {'success': false, 'error': 'No active session'};
    }

    try {
      final response = await http.delete(
        Uri.parse('$baseUrl/chat-history/$sessionId'),
      );

      final responseData = json.decode(response.body);

      if (response.statusCode != 200) {
        return {
          'success': false,
          'error': responseData['detail'] ?? 'Unknown error occurred',
        };
      }

      return {'success': true, 'data': responseData};
    } catch (e) {
      return {'success': false, 'error': e.toString()};
    }
  }
}
