"""
Speech-to-Text engine using Whisper model.
Implements MCP (Model Context Protocol) standards.
"""
import whisper
import os
import logging
import asyncio
from typing import Dict, Any, Optional, List, Union
from ...core.config import settings
from ...utils.audio_utils import record_audio, convert_audio_to_wav

# Configure logger
logger = logging.getLogger("ocularvoice.stt")

class SpeechToTextEngine:
    """
    Speech-to-Text engine using Whisper model.
    Implements MCP (Model Context Protocol) standards.
    """
    
    def __init__(self, model_size: str = None):
        """
        Initialize the Whisper STT engine with the specified model size.

        Args:
            model_size (str, optional): Size of the Whisper model to use.
                                      Options: "tiny", "base", "small", "medium", "large"
                                      If None, uses the size from settings.
        """
        self.model_size = model_size or settings.STT_MODEL_SIZE
        logger.info(f"Loading Whisper model: {self.model_size}")
        self.model = whisper.load_model(self.model_size)
        logger.info(f"Whisper model {self.model_size} loaded successfully")
        
        # Store active streaming sessions
        self._streaming_sessions: Dict[str, Dict[str, Any]] = {}
    
    def status(self) -> Dict[str, Any]:
        """
        Get the status of the STT engine.
        
        Returns:
            Dict: Status information including model version, size, etc.
        """
        return {
            "model": "whisper",
            "model_size": self.model_size,
            "ready": True,
            "version": whisper.__version__,
            "supports_streaming": False,  # Whisper doesn't natively support streaming
            "supported_languages": "multilingual",  # Whisper supports multiple languages
            "active_sessions": len(self._streaming_sessions)
        }
    
    def infer(self, 
              session_id: str, 
              audio_path: str, 
              language: Optional[str] = None,
              task: str = "transcribe") -> Dict[str, Any]:
        """
        Transcribe audio to text using the Whisper model.
        
        Args:
            session_id: Unique session identifier
            audio_path: Path to the audio file to transcribe
            language: Language code to force for transcription (optional)
            task: Task to perform, either "transcribe" or "translate" (to English)
            
        Returns:
            Dict: Transcription result with text, language, and other metadata
        """
        try:
            # Check if file exists
            if not os.path.exists(audio_path):
                logger.error(f"Audio file not found: {audio_path}")
                return {
                    "success": False,
                    "error": f"Audio file not found: {audio_path}",
                    "session_id": session_id
                }

            # Convert audio to WAV format if needed (Whisper works best with WAV)
            file_ext = os.path.splitext(audio_path)[1].lower()
            if file_ext != '.wav':
                logger.info(f"Converting {file_ext} to WAV format")
                wav_path = convert_audio_to_wav(audio_path)
                if wav_path:
                    audio_path = wav_path
                    logger.info(f"Converted to {audio_path}")

            # Set transcription options
            options = {}
            if language:
                options["language"] = language
            
            # Set task (transcribe or translate)
            if task in ["transcribe", "translate"]:
                options["task"] = task

            # Transcribe audio
            logger.info(f"Transcribing audio for session {session_id}: {audio_path}")
            result = self.model.transcribe(audio_path, **options)

            # Log the transcription result
            transcribed_text = result['text']
            logger.info(f"Transcription complete: {transcribed_text[:50]}...")

            # Format response according to MCP
            response = {
                "success": True,
                "session_id": session_id,
                "text": transcribed_text,
                "language": result.get("language"),
                "segments": result.get("segments", []),
                "model": "whisper",
                "model_size": self.model_size
            }
            
            return response
            
        except Exception as e:
            logger.error(f"Error transcribing audio: {str(e)}", exc_info=True)
            return {
                "success": False,
                "error": f"Transcription error: {str(e)}",
                "session_id": session_id
            }
    
    async def stream(self, session_id: str, **kwargs) -> Dict[str, Any]:
        """
        Placeholder for streaming functionality.
        Whisper doesn't natively support streaming, so this returns an error.
        
        Args:
            session_id: Unique session identifier
            **kwargs: Additional parameters
            
        Returns:
            Dict: Error response
        """
        return {
            "success": False,
            "error": "Streaming not supported by Whisper model",
            "session_id": session_id
        }

# Create a singleton instance
stt_engine = SpeechToTextEngine()
