#!/usr/bin/env python3
"""
Simple test script to check if the OcularVoice server is running.
"""
import requests
import time

def test_server():
    """Test if the server is responding."""
    # Test both localhost and network IP
    urls = [
        'http://localhost:8002/',
        'http://127.0.0.1:8002/',
        'http://*************:8002/'
    ]

    for url in urls:
        print(f"Testing {url}...")
        try:
            response = requests.get(url, timeout=5)
            print(f"✅ {url} - Status: {response.status_code}")
            print(f"Response: {response.json()}")
        except requests.exceptions.ConnectionError:
            print(f"❌ {url} - Connection failed")
        except requests.exceptions.Timeout:
            print(f"❌ {url} - Timeout")
        except Exception as e:
            print(f"❌ {url} - Error: {e}")
        print()

    return True

if __name__ == "__main__":
    print("Testing OcularVoice server...")
    if test_server():
        print("✅ Server is running and responding!")
    else:
        print("❌ Server is not responding")
