#!/usr/bin/env python3
"""
Simple test script to check if the OcularVoice server is running.
"""
import requests
import time

def test_server():
    """Test if the server is responding."""
    try:
        # Test the root endpoint
        response = requests.get('http://localhost:8001/', timeout=5)
        print(f"Server response status: {response.status_code}")
        print(f"Server response: {response.json()}")
        return True
    except requests.exceptions.ConnectionError:
        print("Server is not running or not accessible")
        return False
    except requests.exceptions.Timeout:
        print("Server request timed out")
        return False
    except Exception as e:
        print(f"Error testing server: {e}")
        return False

if __name__ == "__main__":
    print("Testing OcularVoice server...")
    if test_server():
        print("✅ Server is running and responding!")
    else:
        print("❌ Server is not responding")
