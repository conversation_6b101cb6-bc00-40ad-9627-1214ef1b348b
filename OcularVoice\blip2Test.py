from transformers import BlipProcessor, BlipForConditionalGeneration
from PIL import Image
import torch

# Load the processor and model
processor = BlipProcessor.from_pretrained("Salesforce/blip-image-captioning-base")
model = BlipForConditionalGeneration.from_pretrained("Salesforce/blip-image-captioning-base")

# Load your image
image_path = "c:\\Users\<USER>\OneDrive\Images\IMG_20230830_170813794.PORTRAIT.jpg"  
raw_image = Image.open(image_path).convert('RGB')

# Preprocess the image
inputs = processor(images=raw_image, return_tensors="pt")

# Generate caption
with torch.no_grad():
    output = model.generate(**inputs)

# Decode the result
caption = processor.decode(output[0], skip_special_tokens=True)
print("Caption:", caption)
