"""
Common utility functions for OcularVoice application.
"""
import os
import uuid
import logging
from typing import Dict, Any, Optional
from datetime import datetime
from fastapi import HTTPException

# Configure logger
logger = logging.getLogger("ocularvoice.utils")

def generate_uuid() -> str:
    """
    Generate a unique UUID string.
    
    Returns:
        str: A UUID string
    """
    return str(uuid.uuid4())

def format_response(
    success: bool, 
    data: Optional[Dict[str, Any]] = None, 
    error: Optional[str] = None,
    session_id: Optional[str] = None
) -> Dict[str, Any]:
    """
    Format a standard API response.
    
    Args:
        success: Whether the request was successful
        data: Optional data to include in the response
        error: Optional error message
        session_id: Optional session ID
        
    Returns:
        Dict: A formatted response dictionary
    """
    response = {
        "success": success,
        "timestamp": datetime.now().isoformat()
    }
    
    if session_id:
        response["session_id"] = session_id
        
    if data:
        response["data"] = data
        
    if error:
        response["error"] = error
        
    return response

def handle_exception(e: Exception, detail: str = "An error occurred") -> None:
    """
    Log an exception and raise an HTTPException.
    
    Args:
        e: The exception that occurred
        detail: A message describing the error
        
    Raises:
        HTTPException: With the provided detail and a 500 status code
    """
    logger.error(f"{detail}: {str(e)}", exc_info=True)
    raise HTTPException(status_code=500, detail=f"{detail}: {str(e)}")

def clean_old_files(directory: str, max_age_days: int = 1) -> None:
    """
    Clean up old files in a directory.
    
    Args:
        directory: The directory to clean
        max_age_days: Maximum age of files in days
    """
    if not os.path.exists(directory):
        return
        
    now = datetime.now()
    count = 0
    
    for filename in os.listdir(directory):
        file_path = os.path.join(directory, filename)
        
        # Skip directories
        if os.path.isdir(file_path):
            continue
            
        # Check file age
        file_modified = datetime.fromtimestamp(os.path.getmtime(file_path))
        age_days = (now - file_modified).days
        
        if age_days >= max_age_days:
            try:
                os.remove(file_path)
                count += 1
            except Exception as e:
                logger.error(f"Error deleting file {file_path}: {str(e)}")
                
    if count > 0:
        logger.info(f"Cleaned up {count} old files from {directory}")
