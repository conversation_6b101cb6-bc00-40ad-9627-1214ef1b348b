"""
Main FastAPI application for OcularVoice.
Implements MCP (Model Context Protocol) standards.
"""
import os
import logging
import async<PERSON>
from fastapi import FastAPI, HTTPException, Request
from fastapi.responses import JSONResponse
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
import uuid
from datetime import datetime

# Import core modules
from .core.config import settings
from .core.context_manager import context_manager

# Import API routes
from .api.routes_stt import router as stt_router
from .api.routes_tts import router as tts_router
from .api.routes_object import router as object_router
from .api.routes_face import router as face_router

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler(settings.LOG_FILE, encoding='utf-8')
    ]
)
logger = logging.getLogger("ocularvoice")

# Initialize FastAPI app
app = FastAPI(
    title=settings.API_TITLE,
    description=settings.API_DESCRIPTION,
    version=settings.API_VERSION,
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.CORS_ORIGINS,
    allow_credentials=settings.CORS_ALLOW_CREDENTIALS,
    allow_methods=settings.CORS_ALLOW_METHODS,
    allow_headers=settings.CORS_ALLOW_HEADERS,
)

# Mount static files for uploads
app.mount("/uploads", StaticFiles(directory=settings.UPLOAD_DIR), name="uploads")

# Include API routers
app.include_router(stt_router)
app.include_router(tts_router)
app.include_router(object_router)
app.include_router(face_router)

# Root endpoint
@app.get("/")
async def root():
    """Root endpoint to check if the server is running"""
    return {
        "message": "OcularVoice API is running",
        "status": "ok",
        "version": settings.API_VERSION,
        "timestamp": datetime.now().isoformat()
    }

# Health check endpoint
@app.get("/health")
async def health():
    """Health check endpoint"""
    return {
        "status": "healthy",
        "timestamp": datetime.now().isoformat()
    }

# Chat history endpoints
@app.get("/chat-history/{session_id}")
async def get_chat_history(session_id: str):
    """
    Get chat history for a specific session
    """
    messages = context_manager.get_chat_history(session_id)
    return {"messages": messages, "session_id": session_id}

@app.delete("/chat-history/{session_id}")
async def clear_chat_history(session_id: str):
    """
    Clear chat history for a specific session
    """
    context_manager.clear_chat_history(session_id)
    return {
        "status": "success",
        "message": f"Chat history for session {session_id} cleared",
        "session_id": session_id
    }

# Legacy endpoints for backward compatibility with the Flutter app
# These will redirect to the new MCP-compliant endpoints

@app.post("/voice-chat/")
async def legacy_voice_chat(request: Request):
    """
    Legacy endpoint for voice chat - redirects to /speech/stt/infer
    """
    logger.warning("Legacy endpoint /voice-chat/ called - redirecting to /speech/stt/infer")
    return await app.url_for("infer")(request)

@app.post("/text-chat/")
async def legacy_text_chat(request: Request):
    """
    Legacy endpoint for text chat - redirects to /speech/tts/infer
    """
    logger.warning("Legacy endpoint /text-chat/ called - redirecting to /speech/tts/infer")
    return await app.url_for("infer_1")(request)

@app.post("/image-analysis/")
async def legacy_image_analysis(request: Request):
    """
    Legacy endpoint for image analysis - redirects to /object_detection/infer
    """
    logger.warning("Legacy endpoint /image-analysis/ called - redirecting to /object_detection/infer")
    return await app.url_for("infer_2")(request)

# Error handlers
@app.exception_handler(HTTPException)
async def http_exception_handler(request, exc):
    """Handle HTTP exceptions"""
    return JSONResponse(
        status_code=exc.status_code,
        content={
            "success": False,
            "error": exc.detail,
            "status_code": exc.status_code
        }
    )

@app.exception_handler(Exception)
async def general_exception_handler(request, exc):
    """Handle general exceptions"""
    logger.error(f"Unhandled exception: {str(exc)}", exc_info=True)
    return JSONResponse(
        status_code=500,
        content={
            "success": False,
            "error": f"Internal server error: {str(exc)}",
            "status_code": 500
        }
    )

# Startup event
@app.on_event("startup")
async def startup_event():
    """Run on application startup"""
    logger.info("Starting OcularVoice API server")
    
    # Clean up old files
    from .utils.common import clean_old_files
    clean_old_files(settings.UPLOAD_DIR)

# Shutdown event
@app.on_event("shutdown")
async def shutdown_event():
    """Run on application shutdown"""
    logger.info("Shutting down OcularVoice API server")
