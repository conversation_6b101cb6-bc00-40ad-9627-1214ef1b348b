"""
Text-to-Speech engine using Edge TTS.
Implements MCP (Model Context Protocol) standards.
"""
import asyncio
import edge_tts
import os
import logging
import tempfile
import sounddevice as sd
import soundfile as sf
from typing import Dict, Any, Optional, List, Union
from ...core.config import settings

# Configure logger
logger = logging.getLogger("ocularvoice.tts")

class TextToSpeechEngine:
    """
    Text-to-Speech engine using Edge TTS.
    Implements MCP (Model Context Protocol) standards.
    """
    
    def __init__(self, voice: str = None):
        """
        Initialize the TTS engine with the specified voice.

        Args:
            voice (str, optional): Voice to use for TTS.
                                 If None, uses the voice from settings.
        """
        self.voice = voice or settings.TTS_VOICE
        logger.info(f"Initialized TTS engine with voice: {self.voice}")
        
        # Store active streaming sessions
        self._streaming_sessions: Dict[str, Dict[str, Any]] = {}
    
    async def get_voices(self) -> List[Dict[str, str]]:
        """
        Get available voices from Edge TTS.
        
        Returns:
            List[Dict]: List of available voices
        """
        try:
            voices = await edge_tts.list_voices()
            return voices
        except Exception as e:
            logger.error(f"Error getting voices: {str(e)}")
            return []
    
    def status(self) -> Dict[str, Any]:
        """
        Get the status of the TTS engine.
        
        Returns:
            Dict: Status information including model version, voice, etc.
        """
        return {
            "model": "edge_tts",
            "voice": self.voice,
            "ready": True,
            "supports_streaming": True,
            "active_sessions": len(self._streaming_sessions)
        }
    
    async def infer(self, 
                   session_id: str, 
                   text: str,
                   voice: Optional[str] = None,
                   output_path: Optional[str] = None) -> Dict[str, Any]:
        """
        Convert text to speech using Edge TTS.
        
        Args:
            session_id: Unique session identifier
            text: Text to convert to speech
            voice: Voice to use (optional, defaults to self.voice)
            output_path: Path to save the audio file (optional)
            
        Returns:
            Dict: Result with output file path
        """
        try:
            # Use provided voice or default
            voice_to_use = voice or self.voice
            
            # Generate output path if not provided
            if not output_path:
                output_path = os.path.join(
                    settings.UPLOAD_DIR, 
                    f"tts_{session_id}_{os.path.basename(tempfile.mktemp(suffix='.mp3'))}"
                )
            
            # Create communicator
            communicate = edge_tts.Communicate(text, voice_to_use)
            
            # Save audio to file
            logger.info(f"Generating TTS for session {session_id} with voice {voice_to_use}")
            await communicate.save(output_path)
            
            logger.info(f"TTS generated and saved to {output_path}")
            
            # Return success response
            return {
                "success": True,
                "session_id": session_id,
                "output_path": output_path,
                "voice": voice_to_use,
                "text_length": len(text)
            }
            
        except Exception as e:
            logger.error(f"Error generating TTS: {str(e)}", exc_info=True)
            return {
                "success": False,
                "error": f"TTS generation error: {str(e)}",
                "session_id": session_id
            }
    
    async def stream(self, 
                    session_id: str, 
                    text: str,
                    voice: Optional[str] = None) -> Dict[str, Any]:
        """
        Stream text to speech using Edge TTS.
        
        Args:
            session_id: Unique session identifier
            text: Text to convert to speech
            voice: Voice to use (optional, defaults to self.voice)
            
        Returns:
            Dict: Result with streaming status
        """
        try:
            # Use provided voice or default
            voice_to_use = voice or self.voice
            
            # Create temporary file for streaming
            temp_file = tempfile.mktemp(suffix=".mp3")
            
            # Create communicator
            communicate = edge_tts.Communicate(text, voice_to_use)
            
            # Save to temporary file
            logger.info(f"Streaming TTS for session {session_id} with voice {voice_to_use}")
            await communicate.save(temp_file)
            
            # Play audio
            data, sr = sf.read(temp_file, dtype='float32')
            sd.play(data, sr)
            
            # Store session info
            self._streaming_sessions[session_id] = {
                "text": text,
                "voice": voice_to_use,
                "temp_file": temp_file,
                "status": "playing"
            }
            
            # Return success response
            return {
                "success": True,
                "session_id": session_id,
                "status": "streaming",
                "voice": voice_to_use,
                "text_length": len(text)
            }
            
        except Exception as e:
            logger.error(f"Error streaming TTS: {str(e)}", exc_info=True)
            return {
                "success": False,
                "error": f"TTS streaming error: {str(e)}",
                "session_id": session_id
            }
    
    def play_audio(self, file_path: str) -> bool:
        """
        Play audio from a file.
        
        Args:
            file_path: Path to the audio file
            
        Returns:
            bool: True if successful, False otherwise
        """
        try:
            data, sr = sf.read(file_path, dtype='float32')
            sd.play(data, sr)
            sd.wait()
            return True
        except Exception as e:
            logger.error(f"Error playing audio: {str(e)}")
            return False

# Create a singleton instance
tts_engine = TextToSpeechEngine()
