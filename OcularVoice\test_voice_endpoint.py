#!/usr/bin/env python3
"""
Test script to check if the voice transcription endpoint is working.
"""
import requests
import tempfile
import wave
import numpy as np

def create_test_audio():
    """Create a simple test audio file."""
    # Create a simple sine wave audio file for testing
    sample_rate = 16000
    duration = 2  # seconds
    frequency = 440  # Hz (A note)
    
    # Generate sine wave
    t = np.linspace(0, duration, int(sample_rate * duration), False)
    audio_data = np.sin(2 * np.pi * frequency * t)
    
    # Convert to 16-bit integers
    audio_data = (audio_data * 32767).astype(np.int16)
    
    # Create temporary WAV file
    temp_file = tempfile.NamedTemporaryFile(suffix='.wav', delete=False)
    
    with wave.open(temp_file.name, 'w') as wav_file:
        wav_file.setnchannels(1)  # Mono
        wav_file.setsampwidth(2)  # 2 bytes per sample
        wav_file.setframerate(sample_rate)
        wav_file.writeframes(audio_data.tobytes())
    
    return temp_file.name

def test_voice_endpoint():
    """Test the voice transcription endpoint."""
    try:
        # Create test audio file
        print("Creating test audio file...")
        audio_file = create_test_audio()
        
        # Test the voice-chat endpoint
        print("Testing voice transcription endpoint...")
        with open(audio_file, 'rb') as f:
            files = {'audio': f}
            data = {'session_id': 'test_session'}
            
            response = requests.post(
                'http://localhost:8001/voice-chat/',
                files=files,
                data=data,
                timeout=30
            )
        
        print(f"Response status: {response.status_code}")
        print(f"Response: {response.json()}")
        
        if response.status_code == 200:
            return True
        else:
            return False
            
    except Exception as e:
        print(f"Error testing voice endpoint: {e}")
        return False

if __name__ == "__main__":
    print("Testing OcularVoice voice transcription endpoint...")
    if test_voice_endpoint():
        print("✅ Voice transcription endpoint is working!")
    else:
        print("❌ Voice transcription endpoint failed")
