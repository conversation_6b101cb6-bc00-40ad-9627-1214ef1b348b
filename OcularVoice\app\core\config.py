"""
Configuration settings for the OcularVoice application.
"""
import os
from pathlib import Path
from typing import Dict, List, Optional, Union
from pydantic import BaseSettings

# Base directory of the application
BASE_DIR = Path(__file__).resolve().parent.parent.parent

# Uploads directory
UPLOAD_DIR = os.path.join(BASE_DIR, "app", "uploads")
os.makedirs(UPLOAD_DIR, exist_ok=True)

# Responses directory for TTS audio files
RESPONSES_DIR = os.path.join(BASE_DIR, "responses")
os.makedirs(RESPONSES_DIR, exist_ok=True)

# Logs directory
LOGS_DIR = os.path.join(BASE_DIR, "logs")
os.makedirs(LOGS_DIR, exist_ok=True)

# Log file path
LOG_FILE = os.path.join(LOGS_DIR, "ocularvoice_server.log")

class Settings(BaseSettings):
    """Application settings."""

    # API settings
    API_TITLE: str = "OcularVoice API"
    API_DESCRIPTION: str = "API for OcularVoice application - voice, text, and image processing"
    API_VERSION: str = "1.0.0"

    # CORS settings
    CORS_ORIGINS: List[str] = ["*"]
    CORS_ALLOW_CREDENTIALS: bool = True
    CORS_ALLOW_METHODS: List[str] = ["*"]
    CORS_ALLOW_HEADERS: List[str] = ["*"]

    # Speech settings
    STT_MODEL_SIZE: str = "base"  # Options: tiny, base, small, medium, large
    TTS_VOICE: str = "en-US-GuyNeural"

    # File settings
    UPLOAD_DIR: str = UPLOAD_DIR
    RESPONSES_DIR: str = RESPONSES_DIR
    TTS_OUTPUT_FILE: str = "response.mp3"

    # Voice options for different languages (male/female)
    VOICE_OPTIONS: Dict[str, Dict[str, str]] = {
        "en": {
            "male": "en-US-GuyNeural",
            "female": "en-US-JennyNeural"
        },
        "fr": {
            "male": "fr-FR-HenriNeural",
            "female": "fr-FR-DeniseNeural"
        },
        "ar": {
            "male": "ar-SA-HamedNeural",
            "female": "ar-SA-ZariyahNeural"
        }
    }

    # Session settings
    SESSION_EXPIRY_MINUTES: int = 60

    class Config:
        env_file = ".env"
        case_sensitive = True

# Create settings instance
settings = Settings()
