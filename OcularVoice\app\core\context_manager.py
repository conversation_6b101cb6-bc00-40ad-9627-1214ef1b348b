"""
Context manager for handling session state in the OcularVoice application.
"""
import uuid
import logging
import json
from typing import Dict, List, Optional, Any
from datetime import datetime, timedelta
from .config import settings

# Configure logger
logger = logging.getLogger("ocularvoice.context")

class ContextManager:
    """
    Manages context for user sessions across different modules.
    
    This class provides methods for:
    - Creating and retrieving session contexts
    - Storing and retrieving chat history
    - Managing session expiry
    """
    
    def __init__(self):
        """Initialize the context manager with empty context storage."""
        # Main context storage: session_id -> context data
        self._contexts: Dict[str, Dict[str, Any]] = {}
        
        # Chat history storage: session_id -> list of messages
        self._chat_history: Dict[str, List[Dict[str, Any]]] = {}
        
        # Session creation times: session_id -> creation timestamp
        self._session_times: Dict[str, datetime] = {}
        
        logger.info("Context manager initialized")
    
    def create_session(self, session_id: Optional[str] = None) -> str:
        """
        Create a new session or retrieve an existing one.
        
        Args:
            session_id: Optional session ID. If not provided, a new UUID will be generated.
            
        Returns:
            str: The session ID (either the provided one or a newly generated one)
        """
        # Generate new session ID if not provided
        if not session_id:
            session_id = str(uuid.uuid4())
            
        # Initialize session context if it doesn't exist
        if session_id not in self._contexts:
            self._contexts[session_id] = {}
            self._chat_history[session_id] = []
            self._session_times[session_id] = datetime.now()
            logger.info(f"Created new session: {session_id}")
        
        return session_id
    
    def get_context(self, session_id: str) -> Dict[str, Any]:
        """
        Get the context for a session.
        
        Args:
            session_id: The session ID
            
        Returns:
            Dict: The session context
        """
        # Create session if it doesn't exist
        if session_id not in self._contexts:
            self.create_session(session_id)
            
        return self._contexts[session_id]
    
    def update_context(self, session_id: str, data: Dict[str, Any]) -> None:
        """
        Update the context for a session.
        
        Args:
            session_id: The session ID
            data: The data to update in the context
        """
        # Create session if it doesn't exist
        if session_id not in self._contexts:
            self.create_session(session_id)
            
        # Update context with new data
        self._contexts[session_id].update(data)
        logger.debug(f"Updated context for session {session_id}")
    
    def add_message(self, session_id: str, role: str, content: str, **kwargs) -> None:
        """
        Add a message to the chat history.
        
        Args:
            session_id: The session ID
            role: The role of the message sender (e.g., "user", "assistant")
            content: The message content
            **kwargs: Additional message metadata
        """
        # Create session if it doesn't exist
        if session_id not in self._chat_history:
            self.create_session(session_id)
            
        # Create message with timestamp and add to history
        message = {
            "role": role,
            "content": content,
            "timestamp": datetime.now().isoformat(),
            **kwargs
        }
        self._chat_history[session_id].append(message)
        logger.debug(f"Added {role} message to session {session_id}")
    
    def get_chat_history(self, session_id: str) -> List[Dict[str, Any]]:
        """
        Get the chat history for a session.
        
        Args:
            session_id: The session ID
            
        Returns:
            List: The chat history
        """
        # Return empty list if session doesn't exist
        if session_id not in self._chat_history:
            return []
            
        return self._chat_history[session_id]
    
    def clear_chat_history(self, session_id: str) -> None:
        """
        Clear the chat history for a session.
        
        Args:
            session_id: The session ID
        """
        if session_id in self._chat_history:
            self._chat_history[session_id] = []
            logger.info(f"Cleared chat history for session {session_id}")
    
    def clear_expired_sessions(self) -> None:
        """Remove expired sessions based on the configured expiry time."""
        now = datetime.now()
        expiry_delta = timedelta(minutes=settings.SESSION_EXPIRY_MINUTES)
        
        expired_sessions = [
            session_id for session_id, timestamp in self._session_times.items()
            if now - timestamp > expiry_delta
        ]
        
        for session_id in expired_sessions:
            if session_id in self._contexts:
                del self._contexts[session_id]
            if session_id in self._chat_history:
                del self._chat_history[session_id]
            if session_id in self._session_times:
                del self._session_times[session_id]
                
        if expired_sessions:
            logger.info(f"Cleared {len(expired_sessions)} expired sessions")

# Create a singleton instance
context_manager = ContextManager()
