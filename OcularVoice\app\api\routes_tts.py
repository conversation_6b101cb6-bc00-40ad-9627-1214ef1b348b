"""
API routes for Text-to-Speech functionality.
Implements MCP (Model Context Protocol) standards.
"""
import os
from fastapi import APIRouter, Form, HTTPException, BackgroundTasks
from fastapi.responses import JSONResponse, FileResponse
from typing import Optional, Dict, Any
import logging

from ..core.context_manager import context_manager
from ..core.config import settings
from ..modules.speech import tts_engine
from ..utils.common import generate_uuid, format_response, handle_exception

# Configure logger
logger = logging.getLogger("ocularvoice.api.tts")

# Create router
router = APIRouter(
    prefix="/speech/tts",
    tags=["text-to-speech"],
    responses={404: {"description": "Not found"}},
)

@router.get("/status")
async def get_status():
    """
    Get the status of the TTS engine.

    Returns:
        Dict: Status information
    """
    try:
        status = tts_engine.status()
        return format_response(success=True, data=status)
    except Exception as e:
        handle_exception(e, "Error getting TTS status")

@router.get("/voices")
async def get_voices():
    """
    Get available voices for TTS.

    Returns:
        Dict: List of available voices
    """
    try:
        voices = await tts_engine.get_voices()
        return format_response(success=True, data={"voices": voices})
    except Exception as e:
        handle_exception(e, "Error getting TTS voices")

@router.get("/voice-options")
async def get_voice_options():
    """
    Get voice options organized by language and gender for the Flutter app.

    Returns:
        Dict: Voice options organized by language and gender
    """
    try:
        return format_response(success=True, data={"voice_options": settings.VOICE_OPTIONS})
    except Exception as e:
        handle_exception(e, "Error getting voice options")

@router.post("/infer")
async def infer(
    text: str = Form(...),
    session_id: Optional[str] = Form(None),
    voice: Optional[str] = Form(None),
    language: Optional[str] = Form("en"),
    gender: Optional[str] = Form("male")
):
    """
    Convert text to speech using the TTS engine.

    Args:
        text: The text to convert to speech
        session_id: Optional session ID
        voice: Optional voice to use (overrides language/gender)
        language: Language code (en, fr, ar)
        gender: Voice gender (male, female)

    Returns:
        Dict: TTS result with file path
    """
    try:
        # Generate or validate session ID
        session_id = context_manager.create_session(session_id)

        # Determine voice to use
        if voice:
            voice_to_use = voice
        else:
            voice_to_use = tts_engine.get_voice_for_language(language, gender)

        # Log the request
        logger.info(f"TTS inference request for session {session_id}, voice: {voice_to_use}, language: {language}, gender: {gender}")

        # Process with TTS engine
        result = await tts_engine.infer(
            session_id=session_id,
            text=text,
            voice=voice_to_use
        )

        # Store TTS result in context
        if result.get("success", False) and "output_path" in result:
            context_manager.update_context(session_id, {
                "last_tts_path": result["output_path"],
                "last_tts_voice": result.get("voice")
            })

            # Add message to chat history
            context_manager.add_message(
                session_id=session_id,
                role="assistant",
                content=text,
                audio_path=result["output_path"],
                voice=result.get("voice")
            )

        return result
    except Exception as e:
        handle_exception(e, "Error processing TTS inference")

@router.post("/stream")
async def stream(
    text: str = Form(...),
    session_id: Optional[str] = Form(None),
    voice: Optional[str] = Form(None)
):
    """
    Stream text to speech using the TTS engine.

    Args:
        text: The text to convert to speech
        session_id: Optional session ID
        voice: Optional voice to use

    Returns:
        Dict: Streaming status
    """
    try:
        # Generate or validate session ID
        session_id = context_manager.create_session(session_id)

        # Log the request
        logger.info(f"TTS streaming request for session {session_id}, voice: {voice}")

        # Process with TTS engine
        result = await tts_engine.stream(
            session_id=session_id,
            text=text,
            voice=voice
        )

        return result
    except Exception as e:
        handle_exception(e, "Error processing TTS streaming")

@router.get("/audio/{session_id}")
async def get_audio(session_id: str):
    """
    Get the audio file for a session.

    Args:
        session_id: The session ID

    Returns:
        FileResponse: The audio file
    """
    try:
        # Get context for session
        context = context_manager.get_context(session_id)

        # Check if TTS path exists
        if "last_tts_path" not in context:
            raise HTTPException(status_code=404, detail="No TTS audio found for this session")

        # Get audio path
        audio_path = context["last_tts_path"]

        # Check if file exists
        if not os.path.exists(audio_path):
            raise HTTPException(status_code=404, detail="Audio file not found")

        # Return file
        return FileResponse(
            audio_path,
            media_type="audio/mpeg",
            filename=os.path.basename(audio_path)
        )
    except HTTPException:
        raise
    except Exception as e:
        handle_exception(e, "Error retrieving TTS audio")
