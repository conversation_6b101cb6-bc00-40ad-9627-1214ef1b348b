"""
Run the OcularVoice API server.
"""
import uvicorn
import os
import sys

if __name__ == "__main__":
    print("Starting OcularVoice server...")
    print("API will be available at http://127.0.0.1:8002")
    print("API documentation will be available at http://127.0.0.1:8002/docs")
    print("Press Ctrl+C to stop the server")

    # Add the current directory to the path so we can import the app module
    sys.path.insert(0, os.path.abspath(os.path.dirname(__file__)))

    # Run the server
    uvicorn.run("main:app", host="0.0.0.0", port=8002, reload=True)
