#!/usr/bin/env python3
"""
Simple test to check server endpoints.
"""
import requests

def test_endpoints():
    """Test various endpoints."""
    base_url = "http://localhost:8001"
    
    # Test root endpoint
    print("Testing root endpoint...")
    try:
        response = requests.get(f"{base_url}/", timeout=5)
        print(f"✅ Root: {response.status_code} - {response.json()}")
    except Exception as e:
        print(f"❌ Root failed: {e}")
    
    # Test docs endpoint
    print("Testing docs endpoint...")
    try:
        response = requests.get(f"{base_url}/docs", timeout=5)
        print(f"✅ Docs: {response.status_code}")
    except Exception as e:
        print(f"❌ Docs failed: {e}")

if __name__ == "__main__":
    test_endpoints()
