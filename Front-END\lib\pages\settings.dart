import 'package:flutter/material.dart';
import 'package:firebase_auth/firebase_auth.dart';
import '../localization/app_localizations.dart';
import 'language_page.dart';
import 'theme_page.dart';
import 'voice_settings_page.dart';

class SettingsPage extends StatelessWidget {
  const SettingsPage({super.key});

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context);
    final theme = Theme.of(context);
    final accentColor =
        theme.brightness == Brightness.light
            ? const Color.fromARGB(255, 17, 77, 180)
            : const Color.fromARGB(255, 64, 123, 255);

    return Scaffold(
      backgroundColor: theme.scaffoldBackgroundColor,
      appBar: PreferredSize(
        preferredSize: const Size.fromHeight(kToolbarHeight),
        child: Container(
          decoration: BoxDecoration(
            color: theme.appBarTheme.backgroundColor,
            boxShadow: [
              BoxShadow(
                color: Colors.black.withAlpha(15),
                blurRadius: 8,
                offset: const Offset(0, 3),
              ),
            ],
          ),
          child: AppBar(
            title: Text(
              localizations.settings,
              style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            backgroundColor: theme.appBarTheme.backgroundColor,
            foregroundColor: accentColor,
            elevation: 0,
            automaticallyImplyLeading: false, // Remove back arrow
          ),
        ),
      ),
      body: SafeArea(
        child: ListView(
          children: [
            // Language option
            ListTile(
              leading: Icon(Icons.language, color: accentColor),
              title: Text(
                localizations.languageText,
                style: TextStyle(color: theme.textTheme.bodyLarge?.color),
              ),
              trailing: Icon(
                Icons.arrow_forward_ios,
                size: 16,
                color: theme.iconTheme.color,
              ),
              onTap: () {
                // Navigate to language selection page
                Navigator.push(
                  context,
                  MaterialPageRoute(builder: (context) => const LanguagePage()),
                );
              },
            ),
            Divider(color: theme.dividerColor),

            // Theme option
            ListTile(
              leading: Icon(Icons.palette, color: accentColor),
              title: Text(
                localizations.themeText,
                style: TextStyle(color: theme.textTheme.bodyLarge?.color),
              ),
              trailing: Icon(
                Icons.arrow_forward_ios,
                size: 16,
                color: theme.iconTheme.color,
              ),
              onTap: () {
                // Navigate to theme selection page
                Navigator.push(
                  context,
                  MaterialPageRoute(builder: (context) => const ThemePage()),
                );
              },
            ),
            Divider(color: theme.dividerColor),

            // Voice settings option
            ListTile(
              leading: Icon(Icons.record_voice_over, color: accentColor),
              title: Text(
                'Voice Settings',
                style: TextStyle(color: theme.textTheme.bodyLarge?.color),
              ),
              trailing: Icon(
                Icons.arrow_forward_ios,
                size: 16,
                color: theme.iconTheme.color,
              ),
              onTap: () {
                // Navigate to voice settings page
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => const VoiceSettingsPage(),
                  ),
                );
              },
            ),
            Divider(color: theme.dividerColor),

            // Sign out option
            ListTile(
              leading: Icon(Icons.logout, color: Colors.red),
              title: Text(
                localizations.signOut,
                style: TextStyle(
                  color: theme.textTheme.bodyLarge?.color,
                  fontWeight: FontWeight.w500,
                ),
              ),
              onTap: () async {
                // Show confirmation dialog
                final bool confirm =
                    await showDialog(
                      context: context,
                      builder:
                          (context) => AlertDialog(
                            title: Text(localizations.signOutConfirmTitle),
                            content: Text(localizations.signOutConfirmMessage),
                            actions: [
                              TextButton(
                                onPressed: () => Navigator.pop(context, false),
                                child: Text(localizations.cancel),
                              ),
                              TextButton(
                                onPressed: () => Navigator.pop(context, true),
                                style: TextButton.styleFrom(
                                  foregroundColor: Colors.red,
                                ),
                                child: Text(localizations.signOut),
                              ),
                            ],
                          ),
                    ) ??
                    false;

                if (confirm) {
                  try {
                    await FirebaseAuth.instance.signOut();
                    // Navigate to login page and remove all previous routes
                    if (context.mounted) {
                      Navigator.pushNamedAndRemoveUntil(
                        context,
                        '/login',
                        (route) => false,
                      );
                    }
                  } catch (e) {
                    if (context.mounted) {
                      ScaffoldMessenger.of(context).showSnackBar(
                        SnackBar(content: Text('Error signing out: $e')),
                      );
                    }
                  }
                }
              },
            ),
            Divider(color: theme.dividerColor),
          ],
        ),
      ),
    );
  }
}
