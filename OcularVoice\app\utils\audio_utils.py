"""
Audio utility functions for OcularVoice application.
"""
import sounddevice as sd
import soundfile as sf
import tempfile
import os
import logging
import subprocess
import uuid
from pydub import AudioSegment
from pathlib import Path
from ..core.config import settings

# Configure logger
logger = logging.getLogger("ocularvoice.audio")

def record_audio(duration=5, sample_rate=16000):
    """
    Record audio from the microphone.

    Args:
        duration (int): Duration to record in seconds
        sample_rate (int): Sample rate for recording

    Returns:
        str: Path to the recorded audio file
    """
    tmpfile = tempfile.mktemp(suffix='.wav')

    logger.info(f"Recording for {duration}s...")
    audio = sd.rec(int(duration * sample_rate), samplerate=sample_rate, channels=1, dtype='float32')
    sd.wait()

    sf.write(tmpfile, audio, sample_rate)
    logger.info(f"Audio recorded to {tmpfile}")
    return tmpfile

def convert_audio_to_wav(audio_path):
    """
    Convert audio file to WAV format for Whisper processing.
    Supports various formats including MP3, M4A, AAC, etc.

    Args:
        audio_path (str): Path to the audio file to convert

    Returns:
        str: Path to the converted WAV file, or None if conversion failed
    """
    try:
        # Get file extension
        file_ext = os.path.splitext(audio_path)[1].lower()

        # If already WAV, return the original path
        if file_ext == '.wav':
            return audio_path

        # Create output path
        output_path = os.path.splitext(audio_path)[0] + '_converted.wav'

        # Try using pydub first (handles many formats)
        try:
            logger.info(f"Converting {audio_path} to WAV using pydub")

            # Load audio based on format
            if file_ext == '.mp3':
                audio = AudioSegment.from_mp3(audio_path)
            elif file_ext in ['.m4a', '.aac']:
                audio = AudioSegment.from_file(audio_path, format='m4a')
            elif file_ext == '.ogg':
                audio = AudioSegment.from_ogg(audio_path)
            elif file_ext == '.flac':
                audio = AudioSegment.from_file(audio_path, format='flac')
            else:
                # Try generic loading
                audio = AudioSegment.from_file(audio_path)

            # Export as WAV
            audio.export(output_path, format='wav')
            logger.info(f"Converted to {output_path}")
            return output_path

        except Exception as pydub_error:
            logger.warning(f"Pydub conversion failed: {str(pydub_error)}")

            # Fallback to ffmpeg if pydub fails
            try:
                logger.info(f"Trying ffmpeg conversion for {audio_path}")
                subprocess.run(
                    ['ffmpeg', '-i', audio_path, '-ar', '16000', '-ac', '1', '-c:a', 'pcm_s16le', output_path],
                    check=True,
                    capture_output=True
                )
                logger.info(f"Converted to {output_path} using ffmpeg")
                return output_path
            except Exception as ffmpeg_error:
                logger.error(f"FFmpeg conversion failed: {str(ffmpeg_error)}")
                return None

    except Exception as e:
        logger.error(f"Error converting audio: {str(e)}")
        return None

def save_uploaded_audio(audio_data, file_ext=".m4a", session_id=None):
    """
    Save uploaded audio data to a file in the uploads directory.
    
    Args:
        audio_data (bytes): The audio data to save
        file_ext (str): The file extension
        session_id (str, optional): The session ID to include in the filename
        
    Returns:
        str: Path to the saved audio file
    """
    # Generate a unique filename
    filename = f"{uuid.uuid4()}"
    if session_id:
        filename = f"{session_id}_{filename}"
    filename = f"{filename}{file_ext}"
    
    # Create full path
    file_path = os.path.join(settings.UPLOAD_DIR, filename)
    
    # Save the file
    with open(file_path, "wb") as f:
        f.write(audio_data)
    
    logger.info(f"Saved uploaded audio to {file_path}")
    return file_path
